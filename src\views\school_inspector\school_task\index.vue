<template>
    <div class="supervisor-main">
      <div class="supervisor-page">
     <!-- <div class="stap">
      <img src="@/assets/img/note/stap.png" class="stap_lt" />
    </div> -->
      <!-- 左侧信息栏 -->
      <aside class="sidebar">
        <div class="profile-card">
          <img class="avatar" :src="userInfo.avatar" alt="avatar" />
          <div class="name">{{ userInfo.nickName }}</div>
          <div class="info">{{ userInfo.gradeText }}  <span style="padding-left: 5px;"></span>|  <span style="padding-right: 5px;"></span> {{ userInfo.termText }}   <span style="padding-left: 5px;"></span>|  <span style="padding-right: 5px;"></span> {{ userInfo.academicText }}</div>
          <div class="school-info-btn">
            <img src="@/assets/img/entranceAssessment/grade.png" width="14px" height="14px" alt="">
            {{ userInfo.orgName }} | {{ userInfo.gradeText }}
          </div>
        </div>
        <div class="textbook-version">教材版本</div>
        <ul class="menu">
          <li v-for="item in subjectList" :key="item.subject" >
            <img 
              :src="getSubjectIcon(item.subject)" 
              :alt="item.subjectName"
              class="subject-icon"
              @error="handleSubjectIconError"
            />
            {{ item.subjectName }}
            <span class="vertical"></span>
            <span class="textbook-link">{{ item.editionName }}</span>
          </li>
        </ul>
      </aside>
  
      <!-- 右侧任务栏 -->
      <main class="task-main-container">

        <!-- 标题区块 -->
        <div class="task-header-block">
        <div class="task-list-header">
          <h2>你当前的督学任务</h2>
          <a href="#" class="view-more"  @click.prevent="goToTaskListPage">查看更多 ></a>
        </div>
        </div>
        
        <!-- 内容区块 -->
        <div class="task-content-block" @scroll="handleScroll" ref="scrollContainer">
          <!-- <div><button class="offline-homework-btn" @click="showOfflineHomeworkDialog">线下作业</button></div> -->
        <div v-if="noTasks" class="no-tasks-placeholder">
          <img width="112px" height="138px" src="@/assets/img/synchronous/empty.png" alt="">
          <p>督学任务空空如也</p>
          
        </div>
        
        <template v-else>
          
          <section v-for="group in displayedTasks" :key="group.subject" class="subject-section">
          <h3 class="subject-title">
            <span class="subject-icon"></span>
            {{ group.subject }}
          </h3>
          
          <div class="task-list-container">

            <div v-for="(taskGroup, groupIndex) in group.taskGroups" :key="groupIndex" class="task-group" >
              <!-- {{ group.taskGroups.length }} -->
                <!-- {{ times > taskGroup.deadline }} -->
              <div class="task-group-header">
                <div class="header-left">
                  <span class="tag lesson-tag" v-if="taskGroup.lesson">{{ taskGroup.lesson }}</span>
                  <span class="tag type-tag">{{ taskGroup.typeName }}</span>
                </div>
                <div class="header-right">
                  <span class="teacher" v-if="taskGroup.teacher">{{ taskGroup.teacher }}</span>
                  <span class="publish-time" v-if="taskGroup.time">布置时间: {{ taskGroup.time }}</span>
                  <span v-if="taskGroup.completedTime" class="completed-time">完成时间: {{ taskGroup.completedTime }}</span>
                  <span v-if="taskGroup.deadline" class="deadline-time">截止时间: {{ taskGroup.deadline }}</span>
                </div>
              </div>
              <div class="task-items"  :class="times > taskGroup.deadline ?'deadline-opacity':''" >          
                 <!-- v-if="shouldShowTask(task) -->
                  
                <div v-for="task in taskGroup.tasks" :key="task.id" class="task-item" @click.prevent="viewTask(task,taskGroup)" > 
                  <!-- {{ shouldShowTask(task) }} -->
                  <!-- {{ task.status }}--{{ task.contentType }} -->
                  <div class="task-item-left" >
                      <img v-if="task.contentType == 7" class="link-img"  width="20px" height="20px" :src="getScoreImage(task.fileType)" alt="">
                      <img v-else-if="task.contentType == 1 || task.contentType == 2" src="@/assets/img/synchronous/bof.png" alt="" srcset="">
                      <img v-else-if="task.contentType == 3 || task.contentType == 4 || task.contentType == 5 || task.contentType == 6" src="@/assets/img/synchronous/docx.png" alt="" srcset="">

                      <span class="task-title">{{ getContentTypeName(task.contentType) }}: {{ task.title }}</span>
                    </div>
                  <div class="task-item-right">
                    <img v-if="task.status == '1' && task.contentType == 1" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <img v-if="task.status == '1' && task.contentType == 2" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <img v-if="task.status == '1' && task.contentType == 3" style="width: 48px;height: 36px;" src="@/assets/img/synchronous/ywc.png" alt="">
                    <div v-if="task.isShowScore && task.score && task.correctStatus" class="task-correctStatus">
                      <div class="correct-rate">{{ task.score }}</div>
                      <div>得分</div>
                    </div>
                    <div v-if="task.isShowScore && task.topicScore" class="task-correctStatus">
                      <div class="correct-rate">{{ task.topicScore }}</div>
                      <div>得分</div>
                    </div>
                    <div v-else-if="task.correctStatus && task.correctRate" class="task-correctStatus">
                      <div class="correct-rate">{{ task.correctRate }}</div>
                      <div>正确率</div>
                    </div>
                    <div v-else-if="!task.correctStatus && task.submitStatus" class="task-correctStatus">
                      <div class="correct-approval" style="color: rgba(239, 157, 25, 1);">待批改</div>
                    </div>
                    <span class="status-badge" v-else :class="task.status"></span>
                    <a href="#" class="go-to-task" @click.prevent="viewTask(task,taskGroup)">去查看 ></a>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </section>
        </template>
        </div>
      </main>
    </div>
    
    <!-- 线下作业弹窗 -->
    <el-dialog
      v-model="offlineHomeworkDialog.visible"
      title="线下作业"
      width="800"
      :center="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      class="offline-homework-dialog"
    >
      <div class="offline-homework-content">
        <div class="teacher-info">
          <span class="teacher-label">{{ offlineDialog.userName }}</span>
          <span class="publish-time" v-if="offlineDialog.prepareFinishTime">布置时间: {{ offlineDialog.prepareFinishTime }}</span>
        </div>
        
        <div class="homework-message">
          <!-- <div class="message-label">老师说了：</div> -->
          <div class="message-content">
            {{ offlineDialog.writtenHomework }}
          </div>
        </div>
        
        <div class="deadline-info" v-if="offlineDialog.endDate">
          <span class="deadline-time">截止时间: {{ offlineDialog.endDate }}</span>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeOfflineHomeworkDialog" class="confirm-btn">我知道了</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 我的资源预览弹窗 -->
    <el-dialog
      v-model="resourcePreviewDialog.visible"
      :title="resourcePreviewDialog.name ? `预览：${resourcePreviewDialog.name}` : '资源预览'"
      width="900"
      :center="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      @close="closeResourcePreviewDialog"
    >
      <template v-if="resourcePreviewDialog.type === 'doc'">
        <iframe :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></iframe>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'pdf'">
        <iframe :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></iframe>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'video'">
        <video :src="resourcePreviewDialog.url" width="100%" height="500" controls style="background:#000"></video>
      </template>
      <template v-else-if="resourcePreviewDialog.type === 'photo'">
        <img :src="resourcePreviewDialog.url" width="100%" height="600" frameborder="0" allowfullscreen></img>
      </template>
      <template v-else>
        <div style="text-align:center;padding:60px 0;color:#888;font-size:16px;">暂不支持该类型资源的预览</div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeResourcePreviewDialog" class="confirm-btn">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    </div>

  </template>
  
  <script lang="ts" setup>
  import { ref, computed, onMounted, onBeforeUnmount,reactive } from 'vue';
  import { useRouter } from 'vue-router';
  import { taskHomepageApi, taskListApi } from "@/api/video"
  import { storeToRefs } from 'pinia'
  import { useUserStore } from "@/store/modules/user"
  import { dataEncrypt } from "@/utils/secret"
  import { subjectEnum3,subjectList} from "@/utils/user/enum"
  import { Action, ElMessage, ElMessageBox } from 'element-plus'

  import { wenke } from '@/utils/user/enum'
  const userStore = useUserStore()
  const { learnNow , chapterObj, subjectObj } = storeToRefs(userStore)
  const router = useRouter();
  const state = reactive({
    visible: false,
    id: ""
  })
  import mp3 from '@/assets/img/synchronous/mp3.png'
  import mp4 from '@/assets/img/synchronous/mp4.png'
  import excel from '@/assets/img/synchronous/excel.png'
  import ppt from '@/assets/img/synchronous/ppt.png'
  import word from '@/assets/img/synchronous/word.png'
  import xl from '@/assets/img/synchronous/xl.png'
  import pdf from '@/assets/img/synchronous/pdf.png'
  import png from '@/assets/img/synchronous/png.png'
  import docx from '@/assets/img/synchronous/docx.png'

  const isWen = computed(()=>{
    return wenke.includes(Number(subjectObj.value.id))
  })
// 根据分数获取对应的图片
const getScoreImage = (score: number) => {
  if (score == 1) return word    
  if (score == 2) return ppt  
  if (score == 3) return excel  
  if (score == 4) return pdf  
  if (score == 8) return mp3  
  if (score == 9) return png  
  if (score == 7) return mp4  
  return docx 
                     
}
  // 线下作业弹窗状态
  const offlineHomeworkDialog = reactive({
    visible: false,
  });

  // 我的资源预览弹窗状态
  const resourcePreviewDialog = reactive({
    visible: false,
    url: '',
    type: '', // 'doc' | 'pdf' | 'video' | 'other'
    name: '',
  });

  // 添加offlineDialog对象，用于存储线下作业相关数据
  const offlineDialog = reactive({
    writtenHomework: '',
    name: '',
    userName: '',
    prepareFinishTime: '',
    endDate: ''
  });

  const times = ref()
  const formattedTime = ref()
  // 支持的文档、视频类型
  const docExts = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  const pdfExts = ['pdf'];
  const videoExts = ['mp4', 'webm', 'ogg'];
  const photo = ['png','jpg','image/jpeg','image','jpeg']

  // 显示线下作业弹窗
  const showOfflineHomeworkDialog = () => {
    offlineHomeworkDialog.visible = true;
  };

  // 关闭线下作业弹窗
  const closeOfflineHomeworkDialog = () => {
    offlineHomeworkDialog.visible = false;
  };

  // 关闭我的资源预览弹窗
  const closeResourcePreviewDialog = () => {
    resourcePreviewDialog.visible = false;
    resourcePreviewDialog.url = '';
    resourcePreviewDialog.type = '';
    resourcePreviewDialog.name = '';
  };

  interface TaskItem {
  id: number;
  type: string;
  title: string;
  status: 'completed' | 'expired';
  contentType:any
}

interface TaskGroup {
  lesson?: string;
  typeName: string;
  teacher?: string;
  time?: string;
  completedTime?: string;
  deadline?: string;
  tasks: TaskItem[];
}

interface SubjectTaskGroup {
  subject: string;
  subjectId: string;
  taskGroups: TaskGroup[];
}
interface TaskGroup {
  lesson?: string;
  typeName: string;
  teacher?: string;
  time?: string;
  completedTime?: string;
  deadline?: string;
  tasks: TaskItem[];
}

const getIcon = (type: 'video' | 'ai' | 'doc' | 'ppt' | 'mp4' | 'jpg' | 'word') => {
  const icons = {
    video: `data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgdmlld0JveD0iMCAwIDI4IDI4Ij4KICA8Y2lyY2xlIGN4PSIxNCIgY3k9IjE0IiByPSIxMyIgZmlsbD0iI0ZGRjdFMSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDE0IDE0KSIvPgogIDxwYXRoIGQ9Ik0xMS41IDguNjY5ODdMMTguNSA4LjY2OTg4VjE5LjMzMDFMMTEuNSAxOS4zMzAxVjguNjY5ODdaIiBmaWxsPSIjRkZGN0UxIi8+CiAgPHBhdGggZD0iTTE5LjI1IDE0TDExLjUgMTguMzMwMXYtOC42NjAyTDE5LjI1IDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+`,
    ai: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
    doc: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM0RUNBQUEiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTExIDEyLjVIMTcuNU0xMSAxNS41SDE2IiBzdHJva2U9IiM0RUNBQUEiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+`,
    ppt: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGRjg3MUMiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHRleHQgeD0iMTAiIHk9IjE2IiBmaWxsPSIjRkY4NzFDIiBmb250LXNpemU9IjYiIGZvbnQtd2VpZ2h0PSI5MDAiPkI8L3RleHQ+Cjwvc3ZnPg==`,
    mp4: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM3NkVCRkYiLz4KPHJlY3QgeD0iOCIgeT0iOSIgd2lkdGg9IjEyIiBoZWlnaHQ9IjEwIiByeD0iMiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTE3Ljc1IDE0TDExLjI1IDE3LjczMlYxMC4yNjhMMTcuNzUgMTRaIiBmaWxsPSIjNzZFQkZGIi8+Cjwvc3ZnPg==`,
    jpg: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiNGQ0M1NEYiLz4KPHBhdGggZD0iTTguNSA5QzguNSA4LjQ0NzcyIDguOTQ3NzIgOCA5LjUgOEgxOC41QzE5LjA1MjMgOCAxOS41IDguNDQ3NzIgMTkuNSA5VjE5QzE5LjUgMTkuNTUyMyAxOS4wNTIzIDIwIDE4LjUgMjBIOS41QzguOTQ3NzIgMjAgOC41IDE5LjU1MjMgOC41IDE5VjlaIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxMSIgY3k9IjEyIiByPSIxIiBmaWxsPSIjRkNDNTRGIi8+CjxwYXRoIGQ9Ik04LjI1IDIwLjI1TDEzLjc1IDEyLjc1TDE5Ljc1IDE3LjI1IiBzdHJva2U9IiNGQ0M1NEYiIHN0cm9rZS13aWR0aD0iMS4yIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+`,
    word: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM1Njk1RkYiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDE1TDEwLjUgMTMuNUwxMSAxNSBNMTIgMTBMMTAuMjUgMTVIMTEuNzVMMTMuNSAxMEgxMloiIGZpbGw9IiM1Njk1RkYiIHN0cm9rZT0iIzU2OTVGRiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE3LjUgMTVMMTYgMTBIMTQuNUwxNi4yNSAxNUgxNy41WiIgZmlsbD0iIzU2OTVGRiIgc3Ryb2tlPSIjNTY5NUZGIiBzdHJva2Utd2lkdGg9IjAuNSIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=`,
    pdf: `data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgiIGhlaWdodD0iMjgiIHZpZXdCb3g9IjAgMCAyOCAyOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTQiIGN5PSIxNCIgcj0iMTMiIGZpbGw9IiM1Njk1RkYiLz4KPHBhdGggZD0iTTguNSA4SDIwQzIwLjU1MjMgOCAyMSA4LjQ0NzcyIDIxIDlWMTlDMjEgMTkuNTUyMyAyMC41NTIzIDIwIDIwIDIwSDguNUM4LjIyMzU1IDIwIDcuNzUgMTkuNTUyMyA3Ljc1IDE5VjguNzVDNy43NSA4LjMyMjg0IDguMDczMSA4IDguNSA4WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTEwIDE1TDEwLjUgMTMuNUwxMSAxNSBNMTIgMTBMMTAuMjUgMTVIMTEuNzVMMTMuNSAxMEgxMloiIGZpbGw9IiM1Njk1RkYiIHN0cm9rZT0iIzU2OTVGRiIgc3Ryb2tlLXdpZHRoPSIwLjUiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE3LjUgMTVMMTYgMTBIMTQuNUwxNi4yNSAxNUgxNy41WiIgZmlsbD0iIzU2OTVGRiIgc3Ryb2tlPSIjNTY5NUZGIiBzdHJva2Utd2lkdGg9IjAuNSIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=`

  };
  return icons[type];
};

  // 年级映射
  const gradeMap: Record<number, string> = {
    1: '一年级',
    2: '二年级', 
    3: '三年级',
    4: '四年级',
    5: '五年级',
    6: '六年级',
    7: '七年级',
    8: '八年级',
    9: '九年级',
    10: '十年级',
    11: '十一年级',
    12: '十二年级'
  };

  // 学期映射
  const termMap: Record<number, string> = {
    1: '上学期',
    2: '下学期'
  };

  // 学制映射
  const academicMap: Record<number, string> = {
    1: '六三制',
    2: '五四制'
  };

  // 用户信息计算属性
  const userInfo = computed(() => {
    if (!learnNow.value) {
      return {
        avatar: 'https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com/education/assets/img/1.0/vip/head-boy.svg',
        nickName: '用户',
        gradeText: '七年级',
        termText: '上学期',
        academicText: '六三制',
        orgName: '学校'
      };
    }
    
    return {
      avatar: learnNow.value.avatar || 'https://xiaoyin-test.obs.cn-south-1.myhuaweicloud.com/education/assets/img/1.0/vip/head-boy.svg',
      nickName: learnNow.value.nickName || '用户',
      gradeText: gradeMap[learnNow.value.gradeId] || '七年级',
      termText: termMap[learnNow.value.termId] || '上学期', 
      academicText: academicMap[learnNow.value.academic] || '六三制',
      orgName: learnNow.value.orgName || '学校'
    };
  });

  // 科目列表计算属性
  const subjectList = computed(() => {
    if (!learnNow.value?.versions) {
      return [];
    }
    
    return learnNow.value.versions.map((version: any) => ({
      subject: version.subject,
      subjectName: version.subjectName,
      editionName: version.editionName,
      bookId: version.bookId
    }));
  });

  const allTasks = ref<SubjectTaskGroup[]>([]);

  // 科目枚举映射
  const subjectMap: Record<string, string> = {
    'math': '数学',
    'chinese': '语文', 
    'english': '英语',
    'physics': '物理',
    'chemistry': '化学',
    'biology': '生物',
    'politics': '道德',
    'history': '历史',
    'geography': '地理'
  };

     // 根据contentType获取图标
   const getTaskIcon = (contentType: number) => {
     const iconMap: Record<number, string> = {
       1: getIcon('video'), // 视频内容
       2: getIcon('doc'),   // 文档内容
       3: getIcon('ai'),    // AI智能内容
       4: getIcon('ppt'),   // 演示文稿
       5: getIcon('mp4'),   // 视频文件
       6: getIcon('jpg'),   // 图片文件
       7: getIcon('word'),  // Word文档
     };
     return iconMap[contentType] || getIcon('doc');
   };

     // 根据type获取任务类型名称
     const getTaskTypeName = (type: number) => {
    const typeMap: Record<number, string> = {
      1: '预习任务',
      2: '随堂作业', 
      3: '复习任务',
      4: '个性化任务'
    };
    return typeMap[type] || '其他任务';
  };

  // 格式化时间
  const formatTime = (timeStr: string) => {
    if (!timeStr) return '';
    const date = new Date(timeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}/${month}/${day} ${hours}:${minutes}`;
  };

  // 计算剩余时间
  const calculateRemainingTime = (finishTime: string) => {
    if (!finishTime) return null;
    
    const now = new Date();
    const endTime = new Date(finishTime);
    const diff = endTime.getTime() - now.getTime();
    
    if (diff <= 0) return null;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);
    
    return {
      hours: String(hours).padStart(2, '0'),
      minutes: String(minutes).padStart(2, '0'), 
      seconds: String(seconds).padStart(2, '0')
    };
  };

  // 判断任务状态（历史任务特有逻辑）
const getTaskStatus = (assignment: any): string => {
  // 如果已有状态，直接使用原始状态
  if (assignment.status) {
    return assignment.status;
  }
  
  // 如果有submitTime说明已完成，否则根据endDate判断是否过期
  if (assignment.submitTime) {
    return 'completed';
  }
  
  if (assignment.endDate) {
    const now = new Date();
    const endDate = new Date(assignment.endDate);
    return now > endDate ? 'expired' : 'completed';
  }
  
  return 'completed'; // 默认为已完成
};
// 创建任务唯一标识的函数
const createTaskUniqueKey = (taskItem: any) => {
  const contentType = taskItem.contentType || 'unknown';
  const name = taskItem.name || taskItem.resourceName || taskItem.title || 'untitled';
  const resourceId = taskItem.resourceId || taskItem.id || '';
  const filePath = taskItem.filePath || '';
  const lessonTitle = taskItem.lessonTitle || '';
  const courseResourceVosLength = taskItem.courseResourceVos?.length || 0;
  const resourceIdsLength = taskItem.resourceIds?.length || 0;

  // 使用更多字段确保唯一性
  return `${contentType}_${name}_${resourceId}_${filePath}_${lessonTitle}_${courseResourceVosLength}_${resourceIdsLength}`.replace(/[^a-zA-Z0-9_]/g, '_');
};

// 通用去重函数
const deduplicateTasks = (tasks: any[], taskType: string = 'unknown') => {
  const uniqueTasks: any[] = [];
  const taskKeys = new Set<string>();

  console.log(`=== ${taskType} 去重分析 ===`);
  tasks.forEach((task: any, index: number) => {
    // 使用统一的唯一标识生成逻辑
    const uniqueKey = task.taskKey || createTaskUniqueKey(task);

    console.log(`任务 ${index + 1}: ${task.name || task.title}`, {
      contentType: task.contentType,
      resourceId: task.resourceId,
      id: task.id,
      filePath: task.filePath,
      uniqueKey: uniqueKey
    });

    if (!taskKeys.has(uniqueKey)) {
      taskKeys.add(uniqueKey);
      uniqueTasks.push(task);
      console.log(`  ✓ 保留任务`);
    } else {
      console.log(`  ✗ 跳过重复任务`);
    }
  });

  console.log(`${taskType}去重结果: 原始${tasks.length}个 -> 去重后${uniqueTasks.length}个`);
  console.log(`=== ${taskType} 去重分析结束 ===`);

  return uniqueTasks;
};

// 转换接口数据为组件数据格式
const transformApiData = (apiData: any[]) => {
  console.log('=== 开始数据转换 ===');
  console.log('接口数据条数:', apiData.length);

  // 统计预习任务数据
  let totalPreviewTasks = 0;
  apiData.forEach((item, index) => {
    if (item.lessonsStepCourseStudyVos && item.lessonsStepCourseStudyVos.length > 0) {
      console.log(`课时 ${index + 1}: ${item.title}`);
      console.log(`  预习任务数量: ${item.lessonsStepCourseStudyVos.length}`);
      totalPreviewTasks += item.lessonsStepCourseStudyVos.length;

      item.lessonsStepCourseStudyVos.forEach((courseItem: any, courseIndex: number) => {
        console.log(`    预习任务 ${courseIndex + 1}:`, {
          name: courseItem.name,
          contentType: courseItem.contentType,
          resourceIds: courseItem.resourceIds?.length || 0,
          courseResourceVos: courseItem.courseResourceVos?.length || 0,
          resourceIdsDetail: courseItem.resourceIds,
          courseResourceVosDetail: courseItem.courseResourceVos?.map((r: any) => ({
            id: r.id,
            resourceName: r.resourceName,
            filePath: r.filePath
          }))
        });
      });
    }
  });
  console.log(`接口总预习任务数: ${totalPreviewTasks}`);

  const subjectTasksMap: Record<string, any> = {};

  // 用于临时存储同一课时的预习任务
  const lessonPreviewTasksMap: Record<string, any> = {};
  
  apiData.forEach(item => {
    // 处理科目名称，去掉数字后缀 (math3 -> math)
    const subjectKey = item.subjectEnum?.replace(/\d+$/, '') || '';
    const subjectName = subjectMap[subjectKey] || subjectKey;
    
    if (!subjectTasksMap[subjectName]) {
      subjectTasksMap[subjectName] = {
        subject: subjectName,
        subjectId: subjectKey,
        taskGroups: []
      };
    }

    // 处理课程学习步骤 (预习任务)
    if (item.lessonsStepCourseStudyVos && item.lessonsStepCourseStudyVos.length > 0) {
      // 创建一个预习任务组的唯一标识
      const lessonKey = `${subjectName}_${item.title}`;

      if (!lessonPreviewTasksMap[lessonKey]) {
        lessonPreviewTasksMap[lessonKey] = {
          ...item,
          contentType: item.contentType,
          lesson: item.title,
          typeName: '预习任务',
          teacher: item.lessonsStepCourseStudyVos[0]?.userName || item.userName,
          time: item.lessonsStepCourseStudyVos[0]?.prepareFinishTime ? formatTime(item.lessonsStepCourseStudyVos[0].prepareFinishTime) : '',
          tasks: [],
          // 添加排序信息
          lessonNumber: extractLessonNumber(item.title),
          taskType: 1 // 预习任务类型为1，确保排在最前面
        };
      }
      
      // 简化处理逻辑：每个courseItem对应一个任务
      item.lessonsStepCourseStudyVos.forEach((courseItem: any, courseIndex: number) => {
        console.log(`处理预习任务 ${courseIndex + 1}: ${courseItem.name}, contentType: ${courseItem.contentType}`);

        // 如果contentType为null，不显示这个层级，而是显示它的子级内容
        if (courseItem.contentType === null || courseItem.contentType === undefined) {
          console.log(`  contentType为null，处理子级内容`, {
            courseResourceVos: courseItem?.courseResourceVos?.length || 0,
            resourceIds: courseItem.resourceIds?.length || 0,
            assignmentsVos: courseItem?.assignmentsVos?.length || 0
          });

          // 处理courseResourceVos子级内容
          if (courseItem?.courseResourceVos && courseItem.courseResourceVos.length > 0) {
            courseItem.courseResourceVos.forEach((resourceItem: any, resourceIndex: number) => {
              const taskKey = `${lessonKey}_preview_${courseIndex}_resource_${resourceIndex}`;

              console.log(`  添加子级资源任务: ${resourceItem.resourceName}, taskKey: ${taskKey}`);

              lessonPreviewTasksMap[lessonKey].tasks.push({
                ...resourceItem,
                id: resourceItem?.id || `resource_${Date.now()}_${resourceIndex}`,
                type: resourceItem?.fileType ? getFileTypeIcon(resourceItem.fileType) : 'word',
                title: resourceItem.resourceName || `资源 ${resourceIndex + 1}`,
                name: resourceItem.resourceName,
                status: resourceItem.status || 'completed',
                contentType: resourceItem.contentType || 7,
                fileType: resourceItem.fileType,
                resourceType: resourceItem?.resourceType,
                filePath: resourceItem.filePath,
                hasParentResources: false,
                taskKey: taskKey
              });
            });
          }

          // 处理resourceIds子级内容
          if (courseItem.resourceIds && courseItem.resourceIds.length > 0) {
            courseItem.resourceIds.forEach((resourceId: string, resourceIndex: number) => {
              const taskKey = `${lessonKey}_preview_${courseIndex}_resourceId_${resourceIndex}`;

              console.log(`  添加子级resourceId任务: ${courseItem.name}, resourceId: ${resourceId}, taskKey: ${taskKey}`);

              lessonPreviewTasksMap[lessonKey].tasks.push({
                ...courseItem,
                id: parseInt(resourceId) || `course_${Date.now()}_${resourceIndex}`,
                type: 'video',
                title: courseItem.name || `课程学习任务 ${resourceIndex + 1}`,
                name: courseItem.name,
                status: courseItem.status || 'completed',
                contentType: 1, // 默认contentType
                resourceType: item?.resourceType,
                resourceId: resourceId,
                hasParentResources: false,
                taskKey: taskKey
              });
            });
          }

          // 处理assignmentsVos子级内容
          if (courseItem?.assignmentsVos && courseItem.assignmentsVos.length > 0) {
            courseItem.assignmentsVos.forEach((assignmentItem: any, assignmentIndex: number) => {
              const taskKey = `${lessonKey}_preview_${courseIndex}_assignment_${assignmentIndex}`;

              console.log(`  添加子级作业任务: ${assignmentItem.name || assignmentItem.title}, taskKey: ${taskKey}`);

              lessonPreviewTasksMap[lessonKey].tasks.push({
                ...assignmentItem,
                id: assignmentItem?.id || `assignment_${Date.now()}_${assignmentIndex}`,
                type: assignmentItem?.fileType ? getFileTypeIcon(assignmentItem.fileType) : 'assignment',
                title: assignmentItem.name || assignmentItem.title || `作业 ${assignmentIndex + 1}`,
                name: assignmentItem.name || assignmentItem.title,
                status: assignmentItem.status || 'completed',
                contentType: assignmentItem.contentType || 2, // 作业类型
                fileType: assignmentItem.fileType,
                resourceType: assignmentItem?.resourceType,
                filePath: assignmentItem.filePath,
                hasParentResources: false,
                taskKey: taskKey
              });
            });
          }
        } else {
          // contentType不为null，正常显示这个层级
          const taskKey = `${lessonKey}_preview_${courseIndex}`;

          console.log(`  contentType不为null (${courseItem.contentType})，正常显示任务: ${courseItem.name}, taskKey: ${taskKey}`);

          // 根据courseItem的结构决定任务的展示方式
          let taskData: any = {
            ...courseItem,
            id: courseItem?.id || `course_${Date.now()}_${courseIndex}`,
            name: courseItem.name,
            title: courseItem.name,
            status: courseItem.status || 'completed',
            contentType: courseItem.contentType,
            resourceType: item?.resourceType,
            hasParentResources: courseItem.contentType !== 7,
            taskKey: taskKey
          };

          // 如果有courseResourceVos，使用第一个资源的信息
          if (courseItem?.courseResourceVos && courseItem.courseResourceVos.length > 0) {
            const firstResource = courseItem.courseResourceVos[0];
            taskData = {
              ...taskData,
              type: firstResource?.fileType ? getFileTypeIcon(firstResource.fileType) : 'word',
              fileType: firstResource.fileType,
              filePath: firstResource.filePath,
              resourceName: firstResource.resourceName,
              title: firstResource.resourceName || courseItem.name
            };
          } else {
            taskData.type = 'video';
          }

          lessonPreviewTasksMap[lessonKey].tasks.push(taskData);
        }


      });
    }

    // 处理座位作业步骤 (课堂随练)
    if (item.lessonsStepSeatWorkVos && item.lessonsStepSeatWorkVos.length > 0) {
      item.lessonsStepSeatWorkVos.forEach((seatWorkItem: any) => {
        const group: any = {
          ...item,
          lesson: item.title,
          typeName: '课堂随练',
          teacher: seatWorkItem.userName || item.userName,
          time: seatWorkItem.prepareFinishTime ? formatTime(seatWorkItem.prepareFinishTime) : '',
          tasks: [],
          // 添加排序信息
          lessonNumber: extractLessonNumber(item.title),
          taskType: 2 // 课堂随练类型为2，排在预习任务之后
        };

        // 处理课堂随练中的作业任务
        if (seatWorkItem.assignmentsVos && seatWorkItem.assignmentsVos.length > 0) {
          seatWorkItem.assignmentsVos.forEach((assignment: any, index: number) => {
            const taskStatus = getTaskStatus(assignment);
            group.tasks.push({
              ...assignment,
              id: assignment.studentPaperId || Date.now() + index,
              type: 'ai',
              title: assignment.assignmentName || `课堂随练${index + 1}`,
              status: taskStatus,
              contentType: seatWorkItem.contentType || 4, // 默认为AI智能作业
            });
          
            // 设置完成时间
            if (assignment.submitTime) {
              group.completedTime = formatTime(assignment.submitTime);
            } else if (assignment.endDate) {
              group.deadline = formatTime(assignment.endDate);
            }
          });
        } else {
          // 如果没有作业任务，创建默认任务
          group.tasks.push({
            ...seatWorkItem,
            id: Date.now(),
            type: 'ai',
            title: seatWorkItem.name || '课堂随练',
            status: seatWorkItem.status || 'completed',
            contentType: seatWorkItem.contentType || 4, // 默认为AI智能作业
          });
        }

        if (group.tasks.length > 0) {
          // 使用通用去重函数
          group.tasks = deduplicateTasks(group.tasks, '课堂随练');
          subjectTasksMap[subjectName].taskGroups.push(group);
        }
      });
    }

    // 处理作业步骤 (复习任务)
    if (item.lessonsStepHomeworkVos && item.lessonsStepHomeworkVos.length > 0) {
      // 创建一个单独的复习任务组，包含所有作业项
      const homeworkGroup: any = {
        ...item,
        lesson: item.title,
        typeName: '复习任务',
        teacher: item.lessonsStepHomeworkVos[0]?.userName || item.userName,
        time: item.lessonsStepHomeworkVos[0]?.prepareFinishTime ? formatTime(item.lessonsStepHomeworkVos[0].prepareFinishTime) : '',
        tasks: [],
        // 添加排序信息
        lessonNumber: extractLessonNumber(item.title),
        taskType: 3 // 复习任务类型为3，排在课堂随练之后
      };

      // 遍历所有复习任务项并添加到同一个组中
      item.lessonsStepHomeworkVos.forEach((homeworkItem: any, index: number) => {
        // 确保类型为3（复习任务）
        homeworkItem.type = 3;
        homeworkGroup.typeName = getTaskTypeName(3) // 固定为复习任务
        homeworkGroup.time = homeworkItem?.prepareFinishTime ? formatTime(homeworkItem?.prepareFinishTime) : ''
        homeworkGroup.teacher = homeworkItem?.userName || item.userName

        // 特殊处理不同contentType
        if (homeworkItem.contentType == 1 || homeworkItem.contentType == 2) {
          // 名师同步学和名师知识点 - 确保显示
          homeworkGroup.tasks.push({
            ...homeworkItem,
            id: homeworkItem.id || Date.now() + Math.random(),
            type: homeworkItem.contentType == 1 ? 'video' : 'doc',
            title: homeworkItem.name || (homeworkItem.contentType == 1 ? '名师同步学' : '名师知识点'),
            status: homeworkItem.status || 'completed',
            contentType: homeworkItem.contentType,
            conType: homeworkItem.type,
            resourceType: item?.resourceType
          });
        }
        // 如果是contentType 7，只显示courseResourceVos数组里的数据，不显示外层
        else if (homeworkItem.contentType == 7) {
          // 只处理courseResourceVos数组
          if (homeworkItem.courseResourceVos && homeworkItem.courseResourceVos.length > 0) {
            // console.log(`处理复习任务资源: ${homeworkItem.name || '未命名复习任务'}, 资源数量: ${homeworkItem.courseResourceVos.length}`);
            
            // 直接添加courseResourceVos中的资源，跳过外层
            homeworkItem.courseResourceVos.forEach((resourceItem: any, index: number) => {
              // 记录每个资源的详细信息
              // console.log(`复习任务资源 ${index + 1}:`, {
              //   id: resourceItem?.id,
              //   name: resourceItem?.resourceName,
              //   fileType: resourceItem?.fileType,
              //   filePath: resourceItem?.filePath
              // });
              
              // 添加标记，表示该资源有父级courseResourceVos
              resourceItem.hasParentResources = true;
              
              homeworkGroup.tasks.push({
                ...resourceItem,
                id: resourceItem?.id || Date.now() + Math.random(),
                type: resourceItem?.fileType ? getFileTypeIcon(resourceItem.fileType) : 'doc',
                title: resourceItem.resourceName || `资源 ${index + 1}`,
                status: resourceItem.status || 'completed',
                contentType: 7, // 我的资源
                fileType: resourceItem.fileType,
                conType: homeworkItem?.type,
                resourceType: resourceItem?.resourceType || item?.resourceType,
                filePath: resourceItem.filePath // 确保保留原始路径信息
              });
            });
          } else {
            // console.log(`复习任务 ${homeworkItem.name || '未命名复习任务'} 没有资源或资源列表为空`);
          }
          
          // 重要：不为contentType 7创建外层容器项
        } 
        // 处理非contentType 7的情况
        else {
          // 处理所有资源ID
          if (homeworkItem.resourceIds && homeworkItem.resourceIds.length > 0) {
            // console.log(`处理复习任务资源: ${homeworkItem.name || '未命名复习任务'}, 资源ID数量: ${homeworkItem.resourceIds.length}`);
            
            // 遍历所有资源ID，为每个资源创建一个任务项
            homeworkItem.resourceIds.forEach((resourceId: string, index: number) => {
              // 对于contentType 7，只有在有父级courseResourceVos时才显示
              const hasParentResources = homeworkItem.contentType !== 7;
              
              homeworkGroup.tasks.push({
                ...homeworkItem,
                id: homeworkItem?.id || parseInt(resourceId) || Date.now() + index,
                type: 'doc',
                title: homeworkItem.name || `资源 ${index + 1}`,
                status: homeworkItem.status || 'completed',
                contentType: homeworkItem.contentType || 7, // 默认为我的资源
                conType: homeworkItem?.type,
                resourceType: item?.resourceType,
                resourceId: resourceId, // 保存原始资源ID
                hasParentResources: hasParentResources // 标记是否有父级resources
              });
            });
          }
          
          // 处理课程资源
          if (homeworkItem.courseResourceVos && homeworkItem.courseResourceVos.length > 0) {
            // console.log(`处理复习任务资源: ${homeworkItem.name || '未命名复习任务'}, 资源数量: ${homeworkItem.courseResourceVos.length}`);
            
            homeworkItem.courseResourceVos.forEach((resourceItem: any, index: number) => {
              // 记录每个资源的详细信息
              // console.log(`复习任务资源 ${index + 1}:`, {
              //   id: resourceItem?.id,
              //   name: resourceItem?.resourceName,
              //   fileType: resourceItem?.fileType,
              //   filePath: resourceItem?.filePath
              // });
              
              // 添加标记，表示该资源有父级courseResourceVos
              resourceItem.hasParentResources = true;
              
              homeworkGroup.tasks.push({
                ...resourceItem,
                id: resourceItem?.id || Date.now() + Math.random(),
                type: resourceItem?.fileType ? getFileTypeIcon(resourceItem.fileType) : 'doc',
                title: resourceItem.resourceName || `资源 ${index + 1}`,
                status: resourceItem.status || 'completed',
                contentType: 7, // 我的资源
                fileType: resourceItem.fileType,
                conType: homeworkItem?.type,
                resourceType: resourceItem?.resourceType || item?.resourceType,
                filePath: resourceItem.filePath // 确保保留原始路径信息
              });
            });
          }
        }

        // 处理课后作业中的作业任务
        if (homeworkItem.assignmentsVos && homeworkItem.assignmentsVos.length > 0) {
          homeworkItem.assignmentsVos.forEach((assignment: any, index: number) => {
            const taskStatus = getTaskStatus(assignment);
            homeworkGroup.tasks.push({
              ...assignment,
              id: assignment.studentPaperId || Date.now() + index,
              type: 'doc',
              title: assignment.assignmentName || `复习任务${index + 1}`,
              status: taskStatus,
              contentType: homeworkItem.contentType || 6, // 默认为真题试卷
              conType: assignment.type,
              resourceType:item?.resourceType
            });
            
            // 设置完成时间或截止时间
            if (assignment.submitTime && !homeworkGroup.completedTime) {
              homeworkGroup.completedTime = formatTime(assignment.submitTime);
            } else if (assignment.endDate && !homeworkGroup.deadline) {
              homeworkGroup.deadline = formatTime(assignment.endDate);
            }
          });
        }

        // 如果没有任何资源或作业，创建默认任务 - 确保每个homeworkItem都有对应的任务
        if (!homeworkItem.resourceIds?.length && !homeworkItem.courseResourceVos?.length && !homeworkItem.assignmentsVos?.length) {
          // 对于contentType 7，只有在有父级courseResourceVos时才显示
          const hasParentResources = homeworkItem.contentType !== 7;
          
          homeworkGroup.tasks.push({
            ...homeworkItem,
            id: Date.now() + homeworkGroup.tasks.length,
            type: 'doc',
            title: homeworkItem.name || `复习任务${homeworkGroup.tasks.length + 1}`,
            status: homeworkItem.status || 'completed',
            contentType: homeworkItem.contentType || 7,
            conType: homeworkItem.type,
            resourceType: item?.resourceType,
            hasParentResources: hasParentResources // 标记是否有父级resources
          });
        }
      });

      // 如果该组中有任务，添加到任务组中
      if (homeworkGroup.tasks.length > 0) {
        // 使用通用去重函数
        homeworkGroup.tasks = deduplicateTasks(homeworkGroup.tasks, '复习任务');
        subjectTasksMap[subjectName].taskGroups.push(homeworkGroup);
      }
    }

    // 处理分配的作业 (个性化任务)
    if (item.assignmentsVos && item.assignmentsVos.length > 0) {
      const group: any = {
        ...item,
        lesson: item.title,
        typeName: '个性化任务',
        teacher: item.userName,
        time: '',
        tasks: [],
        // 添加排序信息
        lessonNumber: 999, // 个性化任务放到最后
        taskType: 4 // 个性化任务类型为4，排在最后
      };

      item.assignmentsVos.forEach((assignment: any, index: number) => {
        const taskStatus = getTaskStatus(assignment);
        group.tasks.push({
          ...assignment,
          id: assignment.id || Date.now() + index,
          type: 'doc',
          title: assignment.assignmentName || '个性化作业',
          contentType: 9,
          status: taskStatus,
          resourceType:item?.resourceType
        });
      });

      if (group.tasks.length > 0) {
        // 使用通用去重函数
        group.tasks = deduplicateTasks(group.tasks, '个性化任务');
        subjectTasksMap[subjectName].taskGroups.push(group);
      }
    }
  });
  
  // 将合并后的预习任务添加到对应科目的任务组中
  Object.keys(lessonPreviewTasksMap).forEach(lessonKey => {
    const previewGroup = lessonPreviewTasksMap[lessonKey];
    const subjectName = previewGroup.subject || lessonKey.split('_')[0];

    if (previewGroup.tasks.length > 0 && subjectTasksMap[subjectName]) {
      console.log(`预习任务组 ${lessonKey} 去重前任务数: ${previewGroup.tasks.length}`);
      // 使用通用去重函数
      const originalCount = previewGroup.tasks.length;
      previewGroup.tasks = deduplicateTasks(previewGroup.tasks, `预习任务-${lessonKey}`);
      console.log(`预习任务组 ${lessonKey} 去重后任务数: ${previewGroup.tasks.length} (原始: ${originalCount})`);
      subjectTasksMap[subjectName].taskGroups.push(previewGroup);
    }
  });

  // 对每个科目的任务组进行排序
  Object.values(subjectTasksMap).forEach((subject: any) => {
    subject.taskGroups.sort((a: any, b: any) => {
      // 首先按课时编号排序
      if (a.lessonNumber !== b.lessonNumber) {
        return a.lessonNumber - b.lessonNumber;
      }
      // 课时编号相同时，按任务类型排序（预习->课堂随练->课后作业->个性化任务）
      return a.taskType - b.taskType;
    });
  });

  // 统计最终结果
  const result = Object.values(subjectTasksMap);
  let finalPreviewTaskCount = 0;
  result.forEach((subject: any) => {
    subject.taskGroups.forEach((group: any) => {
      if (group.typeName === '预习任务') {
        finalPreviewTaskCount += group.tasks.length;
      }
    });
  });

  console.log(`=== 数据转换完成 ===`);
  console.log(`最终预习任务总数: ${finalPreviewTaskCount}`);
  console.log(`接口预习任务总数: ${totalPreviewTasks}`);

  return result;
};

// 从课时标题中提取课时编号
const extractLessonNumber = (title: string): number => {
  if (!title) return 999;
  
  // 匹配"第X课时"或"第X章"等格式
  const match = title.match(/第(\d+)(?:课时|章|单元|节)/);
  if (match && match[1]) {
    return parseInt(match[1]);
  }
  
  // 匹配纯数字
  const numMatch = title.match(/^(\d+)/);
  if (numMatch && numMatch[1]) {
    return parseInt(numMatch[1]);
  }
  
  return 999; // 默认放到最后
};

const getFileTypeIcon=(fileType:string)=>{
  const fileTypeMap={
    '1':'word',
    '2':'ppt',
    '3':'excel',
    '4':'pdf',
    '7':'video',
    '8':'audio',
    '9':'image'
  }
  return fileTypeMap[fileType] || 'doc';
}

  const noTasks = computed(() => allTasks.value.length === 0);

  const goToTaskListPage = () => {
    router.push({ name: 'InspectorTasks' })
  };

  const scrollContainer = ref<HTMLElement | null>(null);
  const displayedTasks = ref<any>([]);
  let taskIndex = 0;
  const tasksPerLoad = 2;

  const loadMore = () => {
    if (taskIndex >= allTasks.value.length) {
      return;
    }
    const nextTasks = allTasks.value.slice(taskIndex, taskIndex + tasksPerLoad);
    displayedTasks.value.push(...nextTasks);
    taskIndex += tasksPerLoad;
  };

  const handleScroll = () => {
    const container = scrollContainer.value;
    if (container) {
      const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10;
      if (isAtBottom) {
        loadMore();
      }
    }
  };

  onMounted(() => {
    // console.log(isWen,"isWenisWenisWenisWen")
    // 记录当前时间
    const now = new Date();
    const formattedTimes = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
    times.value = formattedTimes
    console.log(formattedTimes,"formattedTimeformattedTime")
    
    getTaskHomepage()
    const container = scrollContainer.value;
    if (container) {
        container.addEventListener('scroll', handleScroll);
    }
  });

    // 督学列表
   const getTaskHomepage = async () =>{
     try {
       const res: any = await taskHomepageApi()
       if (res.code === 200) { 
         // 打印原始数据以便调试
        //  console.log('获取到督学任务原始数据:', res.data);
         
         // 验证数据格式
         if (!Array.isArray(res.data)) {
           console.warn('督学任务数据格式错误，预期为数组');
           allTasks.value = [];
           displayedTasks.value = [];
           return;
         }
         
         // 检查资源数据
         let totalResources = 0;
         let contentType7Count = 0;
         let contentType7WithResourcesCount = 0;
         
         res.data.forEach((item: any, index: number) => {
           // 检查课程学习步骤中的资源
           if (item.lessonsStepCourseStudyVos && item.lessonsStepCourseStudyVos.length > 0) {
             item.lessonsStepCourseStudyVos.forEach((courseItem: any, courseIndex: number) => {
               // 统计contentType 7的项目
               if (courseItem.contentType == 7) {
                 contentType7Count++;
                 
                 // 统计有资源的contentType 7项目
                 if (courseItem.courseResourceVos && courseItem.courseResourceVos.length > 0) {
                   contentType7WithResourcesCount++;
                   totalResources += courseItem.courseResourceVos.length;
                  //  console.log(`[${index}][课程${courseIndex}] contentType=7 资源数量: ${courseItem.courseResourceVos.length}`);
                 } else {
                  //  console.log(`[${index}][课程${courseIndex}] contentType=7 但没有资源`);
                 }
               } else if (courseItem.courseResourceVos && courseItem.courseResourceVos.length > 0) {
                 totalResources += courseItem.courseResourceVos.length;
                //  console.log(`[${index}][课程${courseIndex}] 非contentType=7 资源数量: ${courseItem.courseResourceVos.length}`);
               }
             });
           }
           
           // 检查作业步骤中的资源
           if (item.lessonsStepHomeworkVos && item.lessonsStepHomeworkVos.length > 0) {
             item.lessonsStepHomeworkVos.forEach((homeworkItem: any, hwIndex: number) => {
               // 统计contentType 7的项目
               if (homeworkItem.contentType == 7) {
                 contentType7Count++;
                 
                 // 统计有资源的contentType 7项目
                 if (homeworkItem.courseResourceVos && homeworkItem.courseResourceVos.length > 0) {
                   contentType7WithResourcesCount++;
                   totalResources += homeworkItem.courseResourceVos.length;
                  //  console.log(`[${index}][作业${hwIndex}] contentType=7 资源数量: ${homeworkItem.courseResourceVos.length}`);
                 } else {
                  //  console.log(`[${index}][作业${hwIndex}] contentType=7 但没有资源`);
                 }
               } else if (homeworkItem.courseResourceVos && homeworkItem.courseResourceVos.length > 0) {
                 totalResources += homeworkItem.courseResourceVos.length;
                //  console.log(`[${index}][作业${hwIndex}] 非contentType=7 资源数量: ${homeworkItem.courseResourceVos.length}`);
               }
             });
           }
         });       
         
         // 转换接口数据为组件需要的格式
         const transformedData = transformApiData(res.data);
         
         // 统计转换后的任务数量
         let totalTasks = 0;
         transformedData.forEach((subject: any) => {
           subject.taskGroups.forEach((group: any) => {
             totalTasks += group.tasks.length;
           });
         });
        //  console.log(`转换后总任务数量: ${totalTasks}`);
         
         allTasks.value = transformedData;
         
         // 重置分页相关状态
         displayedTasks.value = [];
         taskIndex = 0;
         
         // 初始加载数据
         loadMore();
         
    } else {
         allTasks.value = [];
         displayedTasks.value = [];
       }
     } catch (error) {
       console.error('获取督学任务失败:', error);
       // 出错时使用空数据
       allTasks.value = [];
       displayedTasks.value = [];
     }
   }

  // 判断是否应该显示任务
  const shouldShowTask = (task: any): any => {
    // 如果是contentType 7，检查是否有父级courseResourceVos
    if (task.contentType === 7) {
      // 如果明确标记了hasParentResources为false，则隐藏
      if (task.hasParentResources === false) {
        return false;
      }
      
      // 如果没有明确标记hasParentResources，默认显示非contentType 7的任务
      if (task.hasParentResources === undefined) {
        console.log('未标记的contentType 7任务:', task.title);
        return false;
      }
    }
    return true;
  };

  // 科目图标映射
  const subjectIconMap: Record<string, string> = {
    'math': 'math3.png',
    'chinese': 'yuwenh.png',
    'english': 'english.png',
    'physics': 'wulih.png',
    'chemistry': 'huaxueh.png',
    'bio': 'shengwuh.png',
    'politics': 'zhengzhih.png',
    'history': 'lishih.png',
    'geography': 'dilih.png',
    'science': 'science.png',
    // 添加其他可能的映射
    'yuwen': 'yuwenh.png',
    'shengwu': 'shengwuh.png',
    'huaxue': 'huaxueh.png',
    'shuxue': 'shuxuel.png',
    'wuli': 'wulih.png',
    'lishi': 'lishih.png',
    'dili': 'dilih.png',
    'zhengzhi': 'zhengzhih.png'
  };

  // 获取科目图标
  const getSubjectIcon = (subject: string) => {
    // 处理可能的数字后缀
    const subjectKey = subject?.replace(/\d+$/, '') || '';
    const iconFileName = subjectIconMap[subjectKey];

    if (!iconFileName) {
      console.warn(`未找到科目 ${subject} 对应的图标，可用的科目键: ${Object.keys(subjectIconMap).join(', ')}`);
      return '';
    }
    
    try {
      const iconUrl = new URL(`../../../assets/img/superintendent/${iconFileName}`, import.meta.url).href;
      return iconUrl;
    } catch (error) {
      console.error(`加载科目图标失败: ${subject}`, error);
      return '';
    }
  };

  // 处理科目图标加载错误
  const handleSubjectIconError = (event: Event) => {
    const target = event.target as HTMLImageElement;
    // 隐藏图片，显示默认SVG图标
    target.style.display = 'none';
    
    // 创建默认SVG图标
    const parentElement = target.parentElement;
    if (parentElement && !parentElement.querySelector('.subject-icon-fallback')) {
      const svgElement = document.createElement('span');
      svgElement.className = 'subject-icon-fallback';
      svgElement.innerHTML = `<svg width="16" height="16" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg"><path fill="currentColor" d="M128 160h384v128H128zm0 256h256v128H128zm0 256h512v128H128zm640-384h128v128H768zm0 256h128v128H768zm0-512h128v128H768zM128 64h64v576H128z"/></svg>`;
      parentElement.insertBefore(svgElement, target);
    }
  };

  onBeforeUnmount(() => {
    const container = scrollContainer.value;
    if (container) {
        container.removeEventListener('scroll', handleScroll);
    }
  });

  // 根据contentType获取内容类型中文名称
  const getContentTypeName = (contentType: number) => {
    const contentTypeMap: Record<number, string> = {
      1: '名师同步学',
      2: '名师知识点',
      3: 'AI精准学',
      4: 'AI智能作业',
      5: '选择组卷',
      6: '真题试卷',
      7: '我的资源',
      8: '纸质作业',
      9: '个性化作业'
    };
    return contentTypeMap[contentType] || '';
  };

  // 修改viewTask函数
  const viewTask = (task: any, data: any) => {
    // 记录完整的任务数据，方便调试
    // console.log('查看任务详情:', {
    //   contentType: task.contentType,
    //   title: task.title,
    //   id: task.id,
    //   fileType: task.fileType,
    //   filePath: task.filePath,
    //   resourceId: task.resourceId
    // });

    const status = task.contentType;
    const subjectData = subjectEnum3?.find(item=>data.subjectEnum.includes(item.key))
    console.log(status,"status",task.reviewerType,task,"tasktask",task?.score,task.moduleType,"subjectEnum")   

    switch (status) {
    case 1:
        router.push({
          name: "SynchronousList",
          query: {
            bookId: task?.yxpBookId,
            // courseTitle: data?.title,
            yxpChapterIds: task?.yxpChapterIds?.join(','),
            resourceIds: task?.resourceIds?.join(','),
            subject: data?.subjectEnum,
            source:"historyTask",
            moduleType:task?.moduleType
          }
        });
        break
        case 2:
        router.push({
          name: "TeachRoomTeachList",
          query: {
            subName: subjectData?.text,
            bookId: task?.bookId,
            chapterId: task?.chapterId,
            videoIds: task?.videoIds?.join(','),
            source:"historyTask"
          }
        });
      break
      case 3:
        if ( task.subjectEnum == 'chinese3'){
          router.push({
            name: 'Minesweeper',
            query: {
              // subject: subjectData?.key?subjectList[subjectData?.key]?.key:11,
              bookId: task?.bookId,
              resourceIds: task?.resourceIds?.join(','),
              contentType:"historyTask",
              chapterId:task?.chapterId
            }
          });
        }else{
            userStore.setChapterId(task?.id, task?.name,'同步模式')
            localStorage.setItem('selectedChapterInfo_synchronous', JSON.stringify({id:task?.id.value,name:task?.name}))
            router.push({
            name: 'KnowledgeGraphDetail',
            query: {
              subject: subjectData?.key?subjectList[subjectData?.key]?.key:11,
              dataType: 1,
              fiveType: task?.conType,
              type: 'synchronous',
              bookId: task?.bookId,
              resourceIds: task?.resourceIds?.join(','),
              contentType:"historyTask",
              chapterId:task?.chapterId
            }
          });
        }

      break
      case 4:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
                data: dataEncrypt({
                  studentPaperId: task.studentPaperId,
                  title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName,
                reviewerType:task?.reviewerType,
                schoolId:task?.studentPaperId
              }),
            }
          })
        }
      break
      case 5:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              studentPaperId: task.studentPaperId,
              title: task?.assignmentName
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName,
                reviewerType:task?.reviewerType,
                schoolId:task?.studentPaperId
              }),
            }
          })
        }
      break
        case 6:
      if (task?.correctRate) {
          router.push({
            name: 'Analysis',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName,
                reviewerType:task?.reviewerType,
                schoolId:task?.studentPaperId
              }),
            }
          })
        }
      break
        case 7: {
        // 预览我的资源弹窗
        let filePath = task?.filePath;
        
        // 记录资源访问详情，便于调试
        // console.log('正在访问资源:', {
        //   filePath: filePath,
        //   resourceId: task.resourceId,
        //   fileType: task.fileType,
        //   title: task.title
        // });
        
        // 如果没有直接的filePath，尝试从其他属性获取
        if (!filePath && task.resourceId) {
          // console.log(`尝试通过resourceId(${task.resourceId})查找文件路径`);
          // 这里可以添加API调用来获取资源详情，如果有的话
        }
        
        if (!filePath) {
          ElMessage({
            message: '资源路径不存在，无法预览',
            type: 'warning',
            duration: 2000
          });
          return;
        }
        
        const url = filePath;
        const extMatch = url.match(/\.([a-zA-Z0-9]+)(\?.*)?$/);
        const ext = extMatch ? extMatch[1].toLowerCase() : '';
        
        // console.log(`资源类型: ${ext}, 文件路径: ${url}`);
        if (docExts.includes(ext)) {
          // office文档
          const encodedUrl = encodeURIComponent(url);
          resourcePreviewDialog.url = `https://view.officeapps.live.com/op/embed.aspx?src=${encodedUrl}`;
          resourcePreviewDialog.type = 'doc';
        } else if (pdfExts.includes(ext)) {
          window.open(url, '_blank')
          return 
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'pdf';
        } else if (videoExts.includes(ext)) {
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'video';
        }else if(photo.includes(ext)){
          resourcePreviewDialog.url = url;
          resourcePreviewDialog.type = 'photo';
        }else {
          resourcePreviewDialog.url = '';
          resourcePreviewDialog.type = 'other';
        }
        resourcePreviewDialog.visible = true;
        resourcePreviewDialog.name = task?.title || '';
        break;
      }
      case 8:

      offlineDialog.writtenHomework = task?.writtenHomework || '暂无作业内容';
      offlineDialog.userName = task?.userName || '老师';
      offlineDialog.prepareFinishTime = task?.prepareFinishTime ? formatTime(task.prepareFinishTime) : '';
      offlineDialog.endDate = task?.endDate ? formatTime(task.endDate) : '';
      showOfflineHomeworkDialog();
        break
        case 9:
      if (task?.correctRate && task?.score && task?.correctStatus) {
          router.push({
            name: 'Analysis',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName
              }),
            }
          })
        }else if(task?.submitStatus){
            ElMessage({
              message: "此份作业客观题已批改，主观题老师需要你或家长对着答案自己批改！",
              type: 'success'
          })
        }else { 
          router.push({
            name: 'Assignment',
            query: {
              data: dataEncrypt({
                studentPaperId: task.studentPaperId,
                title: task?.assignmentName,
                reviewerType:task?.reviewerType,
                schoolId:task?.studentPaperId
              }),
            }
          })
        }
      break
      default:
      router.push('/school_inspector/tasks');
      break
    }
  
  // 1: '名师同步学',
  //   2: '名师知识点',
  //   3: 'AI精准学',
  //   4: 'AI智能作业',
  //   5: '选择组卷',
  //   6: '真题试卷',
  //   7: '我的资源',
  //   8: '纸质作业'
  };
  </script>
  
<style lang="scss" scoped>
.correct-approval{
  position: relative;
}
.correct-approval::before{
  content: '';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -10px;
  border-radius: 14px;
  background: rgba(239, 157, 25, 1);
  width: 4px;
  height: 4px;
}
.supervisor-main {
    width: 100vw;
  height: 100vh;
    background: #f0f2f5;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  box-sizing: border-box;
}

  .supervisor-page {
  width: 100%;
  max-width: 1300px;
  height: 100%;
  display: flex;
    position: relative;
}

.stap {
  position: absolute;
  top: -2px;
  left: 20px;
  width: calc(100% - 40px);
    display: flex;
  justify-content: space-between;
  z-index: 1;
  pointer-events: none;

  img {
    height: 40px;
  }
  .stap_lt {
    margin-left: 230px;
  }
  .stap_rt {
    margin-right: -10px;
  }
}

  .sidebar {
  width: 346px;
  flex-shrink: 0;
  background: white;
  padding: 20px;
  border-radius: 20px;
  margin-right: 10px;
  height: 100%;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.05);

  .profile-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 20px;
    border-bottom: 1px solid #EAEAEA;
  .avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 10px;
  }

  .name {
      font-weight: 400;
      font-size: 16px;
    }

  .info {
      font-size: 12px;
      color: #666;
      margin: 5px 0;
      padding-top: 10px;
    }

  .school-info-btn {
      width:90%;
      background-color: #EEF3FD;
      border: none;
      padding: 8px 10px;
      border-radius: 15px;
      font-size: 12px;
      color: #5A85EC;
      margin-top: 5px;
      text-align: center;
      margin-bottom: 40px;
      svg {
        margin-right: 5px;
      }
    }
  }

  .textbook-version {
    padding-top: 10px;
    color: #333;
    font-size: 14px;
    margin-bottom: 10px;
  }

  .menu {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
    display: flex;
    align-items: center;
      padding: 15px 10px;
      cursor: pointer;
      border-radius: 5px;
    font-size: 14px;
      color: #333;
      transition: background-color 0.3s;

      // &:hover,
      // &.active {
      //   background-color: #f0f7ff;
      //   color: #4a90e2;
      // }

      .subject-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        object-fit: contain;
        vertical-align: middle;
        transition: transform 0.2s ease;
        
        &:hover {
          transform: scale(1.1);
        }
      }
      
      .subject-icon-fallback {
        margin-right: 10px;
        display: inline-flex;
        align-items: center;
        
        svg {
          fill: currentColor;
          margin-right: 0;
        }
      }

      .vertical {
        border-left: 1px solid #e0e0e0;
        height: 16px;
        margin: 0 10px;
      }
      .textbook-link {
        color: #5a85ec;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.task-main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.task-header-block {
  background-color: #ffffff;
  padding: 23px 20px;
  border-radius: 20px 20px 0 0;
  margin-bottom: 10px;
  flex-shrink: 0;
  border: 1px solid #eaeaea;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

  .task-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  
  h2 {
    margin: 0;
    font-size: 16px;
  }

  .view-more {
    color: rgba(0, 156, 127, 1);
    text-decoration: none;
    font-size: 16px;
  }
}

.task-content-block {
  flex: 1;
  background-color: #ffffff;
  // padding: 20px;
  // border-radius: 8px;
  overflow-y: auto;
  // box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  // border: 1px solid #eaeaea;
  
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  
  .offline-homework-btn {
    margin: 15px 20px;
    background-color: #fff7e1;
    border: 1px solid #ffd666;
    color: #d48806;
    font-weight: bold;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    display: flex;
    align-items: center;
    
    &:hover {
      background-color: #ffd666;
      color: #fff;
    }
    
    &:active {
      transform: scale(0.98);
    }
    
    &::before {
      content: "";
      display: inline-block;
      width: 16px;
      height: 16px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
      margin-right: 6px;
    }
  }
}

.no-tasks-placeholder {
  text-align: center;
  padding-top: 280px;
  color: #999;
  p {
    font-size: 16px;
  }
}

.subject-section {
  margin-bottom: 25px;
  // box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.06);
  border: 1px solid #eaeaea;
  background-color: #fff;
  // border-radius: 8px;
  // padding: 20px;
  &:last-of-type {
    margin-bottom: 0;
  }
}

.subject-title {
  padding: 0;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  color: #333;
  background: #eef3fd;
  padding: 10px 15px 10px 0;

  .subject-icon {
    display: inline-block;
    width: 14px;
    height: 16px;
    background-color: #4a90e2;
    margin-right: 12px;
    border-radius: 0px 16px 16px 0;
  }
}

.task-list-container {
  padding-left: 18px;
  padding-bottom: 10px;
  padding-right: 10px;
}

.task-group {
    margin-bottom: 20px;
}

.task-group-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    padding-right: 10px;

    .header-left {
        display: flex;
        align-items: center;
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .teacher {
        color: #666;
        font-size: 13px;
      }
      
      .publish-time {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        background-color: #FFFBE6;
        color: #D48806;
        white-space: nowrap;
      }

      .completed-time {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        background-color: #E6FFFB;
        color: #13C2C2;
        white-space: nowrap;
      }

      .deadline-time {
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
        background-color: #FFF1F0;
        color: #CF1322;
        white-space: nowrap;
      }
    }


  .tag {
    padding: 3px 12px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
    border: 1px solid transparent;
  }

  .lesson-tag {
    border-color: #4a90e2;
    color: #4a90e2;
    background-color: #fff;
  }

  .type-tag {
    border-color: #eef3fd;
    color: rgba(90, 133, 236, 1);
    background-color: #eef3fd;
  }
}

.task-items {
  background-color: #fafbfc;
  border-radius: 8px;
  padding: 0px 20px;
  border: 1px solid #f0f0f0;
}

.task-items.deadline-opacity {
  opacity: .5;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2px 0;
    border-bottom: 1px dashed #e8e8e8;
    transition: all 0.2s ease-in-out;
    height: 55px;

    &:last-child {
        border-bottom: none;
    }

    &:hover {
      cursor: pointer;
      background-color: #f1f1f1;
      margin: 0 -20px;
      padding: 2px 20px;
      border-radius: 4px;
      border-bottom-color: transparent;
    }
}

.task-item-left {
    display: flex;
    align-items: center;
    gap: 15px;
    img{
      width: 20px;
      height: 20px;
    }
    .task-icon {
        width: 28px;
        height: 28px;
        flex-shrink: 0;
    }

    .task-title {
        font-size: 14px;
        color: #333;
    }
}

.task-item-right {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 13px;
    color: #888;
    flex-shrink: 0;



    .tag-like {
      padding: 4px 10px;
      border-radius: 12px;
      font-size: 12px;
      white-space: nowrap;
    }

    .go-to-task {
        color: rgba(0, 156, 127, 1);
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        margin-left: 10px;
    }
}

/* 线下作业弹窗样式 */
.offline-homework-dialog {
  .el-dialog__header {
    background-color: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 15px 20px;
    position: relative;
    text-align: center;
    margin-right: 0;
    
    .el-dialog__title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
    
    .el-dialog__headerbtn {
      top: 15px;
    }
  }

  .el-dialog__body {
    padding: 20px 30px;
    text-align: left;
  }

  .offline-homework-content {
    .teacher-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px dashed #eee;
      padding-top: 20px;
      .teacher-label {
        font-weight: bold;
        color: #333;
        font-size: 15px;
      }

      .publish-time {
        font-size: 13px;
        color: #666;
      }
    }

    .homework-message {
      background-color: #f5f5f5;
      border-radius: 4px;
      padding: 15px 20px;
      margin: 15px 0;
      height: 300px;
      
      .message-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
        font-size: 15px;
      }
      
      .message-content {
        color: #333;
        line-height: 1.6;
        font-size: 14px;
      }
    }

    .deadline-info {
      text-align: right;
      padding-top: 10px;
      
      .deadline-time {
        display: inline-block;
        background-color: #FFF1F0;
        color: #CF1322;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 12px;
      }
    }
  }

  .el-dialog__footer {
    background-color: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 15px 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
    
    .confirm-btn {
      background-color: #10b981;
      color: white;
      border: none;
      padding: 8px 30px;
      border-radius: 4px;
      font-size: 14px;
      font-weight: normal;
      
      &:hover {
        background-color: #0ea271;
      }
    }
  }
}
  </style>